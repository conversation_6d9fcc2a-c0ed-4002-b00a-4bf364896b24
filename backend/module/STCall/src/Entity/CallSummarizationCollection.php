<?php

declare(strict_types=1);

namespace STCall\Entity;

class CommentCollection extends \STLib\Expand\Collection
{
    /**
     *
     * @param mixed $comment
     * @param string|int|null $key
     * @return \STLib\Expand\Collection
     * @throws \RuntimeException
     */
    public function add(mixed $comment, string|int|null $key = null): \STLib\Expand\Collection
    {
        if (!($comment instanceof Comment)) {
            throw new \RuntimeException('Comment must be an instace of "\STCall\Entity\Comment"');
        }
        parent::add($comment, $key ?? $comment->getCommentId());
        return $this;
    }

    /**
     *
     * @return array
     */
    public function getMentionedUserIds(): array
    {
        $mentionedUserIds = [];
        foreach ($this as $comment) {
            $mentionedUserIds = array_merge($mentionedUserIds, $comment->getMentionedUserIds());
        }
        return array_unique($mentionedUserIds);
    }

    /**
     *
     * @return array
     */
    public function getFlatComments(): array
    {
        $result = [];
        foreach ($this as $comment) {
            $result[] = $comment->getUser()->getName() . ': ' . $comment->getMessageBodyWithMentions();
        }
        return $result;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = [];
        foreach ($this as $comment) {
            $result[] = $comment->toArray();
        }
        return $result;
    }
}
