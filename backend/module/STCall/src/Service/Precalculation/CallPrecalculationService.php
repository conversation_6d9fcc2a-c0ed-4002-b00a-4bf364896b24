<?php

declare(strict_types=1);

namespace STCall\Service\Precalculation;

use Carbon\Carbon;
use STCall\Entity\Call;
use STCall\Entity\EventHappening;
use STCall\Entity\Paragraph;
use STCall\Service\CallSummarizationSelectorService;
use STCompany\Entity\Event\Event;

class CallPrecalculationService
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;
    use \STClickhouse\Data\QueriesTrait;

    /**
     *
     * @var \STCompany\Service\EventService
     */
    protected \STCompany\Service\EventService $eventService;

    /**
     *
     * @var \STCall\Service\CallService
     */
    protected \STCall\Service\CallService $callService;

    /**
     *
     * @var \STCompany\Service\RoleService
     */
    protected \STCompany\Service\RoleService $roleService;

    /**
     *
     * @var \STCompany\Data\CompaniesTable
     */
    protected \STCompany\Data\CompaniesTable $companiesTable;

    /**
     *
     * @var \STCall\Data\PrecalculatedCallsTable
     */
    protected \STCall\Data\PrecalculatedCallsTable $precalculatedCallsTable;

    /**
     *
     * @var \STCall\Data\PrecalculatedCallsEventsTable
     */
    protected \STCall\Data\PrecalculatedCallsEventsTable $precalculatedCallsEventsTable;

    protected CallSummarizationSelectorService $callSummarizationSelector;

    /**
     *
     * @param \STCompany\Service\EventService $eventService
     * @param \STCall\Service\CallService $callService
     * @param \STCompany\Service\RoleService $roleService
     * @param \STCompany\Data\CompaniesTable $companiesTable
     * @param \STCall\Data\PrecalculatedCallsTable $precalculatedCallsTable
     * @param \STCall\Data\PrecalculatedCallsEventsTable $precalculatedCallsEventsTable
     * @param CallSummarizationSelectorService $callSummarizationSelector
     */
    public function __construct(
        \STCompany\Service\EventService $eventService,
        \STCall\Service\CallService $callService,
        \STCompany\Service\RoleService $roleService,
        \STCompany\Data\CompaniesTable $companiesTable,
        \STCall\Data\PrecalculatedCallsTable $precalculatedCallsTable,
        \STCall\Data\PrecalculatedCallsEventsTable $precalculatedCallsEventsTable,
        CallSummarizationSelectorService $callSummarizationSelector,
    ) {
        $this->eventService = $eventService;
        $this->callService = $callService;
        $this->roleService = $roleService;
        $this->companiesTable = $companiesTable;
        $this->precalculatedCallsTable = $precalculatedCallsTable;
        $this->precalculatedCallsEventsTable = $precalculatedCallsEventsTable;
        $this->callSummarizationSelector = $callSummarizationSelector;
    }

    /**
     *
     * @param int $companyId
     * @param array $callIds
     * @param int|array|null $roleId
     * @return bool
     * @throws \ReflectionException
     * @throws \STApi\Entity\Exception\NotFoundApiException
     */
    public function precalculateCalls(
        int $companyId,
        array $callIds,
        int|array|null $roleId = null,
    ): bool {
        $companyData = $this->companiesTable->getCompany($companyId);
        /** @var \STCompany\Entity\Company $company */
        $company = $this->hydrate((array) $companyData->current(), \STCompany\Entity\Company::class);

        $roleIds = match (strtolower(gettype($roleId))) {
            'null' => $this->roleService->getRoleIds($company->getId()),
            'integer' => [$roleId],
            'array' => $roleId,
        };
        $defaultFragmentColor = $this->eventService->getColor(\STCompany\Data\EventsColorsTable::GREY_COLOR_ID);
        $eventCategories = $this->eventService->getCategories($company->getId());
        $eventCollection = $this->eventService->getEvents($company->getId());
        $callRoleCollection = $this->callService->getCallsWithRoleIds($company, $roleIds, $callIds, $eventCollection, $defaultFragmentColor);

        $callPrecalculated = [];
        $eventsPrecalculated = [];
        foreach ($callRoleCollection as $callRole) {
            $call = $callRole->getCall();
            $callPrecalculated[] = $this->getPrecalculatedCallRecord($callRole->getRoleId(), $call);
            $eventsPrecalculated = array_merge(
                $eventsPrecalculated,
                $this->getPrecalculatedEventsRecord($callRole->getRoleId(), $callRole->getCall(), $eventCategories)
            );
        }

        $eventsPrecalculated = array_merge(
            $eventsPrecalculated,
            $this->reachByDeletedExistedPrecalculatedEventsRecord($company, $roleIds, $callIds, $eventsPrecalculated)
        );

        $this->precalculatedCallsTable->save($callPrecalculated);
        $this->precalculatedCallsEventsTable->save($eventsPrecalculated);
        return true;
    }

    /**
     * @param Event $event
     * @param Carbon|null $minimalReviewTime
     * @param int $limit
     * @param bool $movedToNeutral
     * @return array
     */
    public function getReviewedCallCandidatesIdsWithEvent(Event $event, ?Carbon $minimalReviewTime, int $limit = 10, bool $movedToNeutral = false): array
    {
        return $this->precalculatedCallsEventsTable->getReviewedCallCandidatesIdsWithEvent($event, $minimalReviewTime, $limit, $movedToNeutral);
    }

    /**
     * @param \STCompany\Entity\Company $company
     * @param Event $event
     * @param array $callIds
     * @param bool $movedToNeutral
     * @return array
     */
    public function getPrecalculatedCallsEvents(
        \STCompany\Entity\Company $company,
        Event $event,
        array $callIds,
        bool $movedToNeutral = false
    ): array {
        return $this->precalculatedCallsEventsTable->getPrecalculatedCallsEvents($company, $event, $callIds, $movedToNeutral);
    }

    /**
     * @param string $callId
     * @param int $companyId
     * @return int
     */
    public function getPrecalculatedCallsCount(string $callId, int $companyId): int
    {
        return $this->precalculatedCallsTable->getPrecalculatedCallsCount($callId, $companyId);
    }

    /**
     * @param string $callId
     * @param int $companyId
     * @return int
     */
    public function getPrecalalculatedEventsCount(string $callId, int $companyId): int
    {
        return $this->precalculatedCallsEventsTable->getPrecalculatedEventsCount($callId, $companyId);
    }

    public function deleteByCallsIds(array $callsIds): void
    {
        if (empty($callsIds)) {
            return;
        }

        $this->precalculatedCallsTable->deleteByCallsIds($callsIds);
    }

    /**
     * Extract conversation type from CallSummarization entity
     *
     * @param Call $call
     * @return string|null
     */
    protected function getConversationType(Call $call): ?string
    {
        try {
            $summarizationData = $this->callSummarizationSelector->getExtendedCallSummarization(
                $call->getId(),
                $call->getCompanyId()
            );

            if (empty($summarizationData) || !isset($summarizationData['conversation_type'])) {
                return null;
            }

            $conversationType = $summarizationData['conversation_type'];

            // Extract text between ** symbols using regex
            if (preg_match('/\*\*([^*]+)\*\*/', $conversationType, $matches)) {
                return trim($matches[1]);
            }

            return null;
        } catch (\Exception $e) {
            // Log error if needed, but don't break the precalculation process
            return null;
        }
    }

    /**
     * @param int $roleId
     * @param Call $call
     * @return array
     */
    protected function getPrecalculatedCallRecord(int $roleId, Call $call): array
    {
        $conversationType = $this->getConversationType($call);

        return [
            'company_id' => $call->getCompanyId(),
            'role_id' => $roleId,
            'call_id' => $call->getId(),
            'agent_id' => $call->getAgentId(),
            'client_id' => $call->getClientId(),
            'call_origin' => $call->getOrigin(),
            'call_language' => $call->getLanguage(),
            'call_type' => $call->getCallType(),
            'call_status' => $call->getCallStatus(),
            'is_analyzed' => $call->isAnalyzed(),
            'call_duration' => $call->getDuration(),
            'call_time' => $call->getTime(),
            'score' => $call->getScore(),
            'is_reviewed' => $call->isReviewed(),
            'reviewed_time' => $call->getReviewedTime(),
            'is_partly_reviewed' => $call->isPartlyReviewed(),
            'uploaded_time' => $call->getUploadedTime(),
            'reviewer_user_id' => $call->getReviewerUserId(),
            'event_ids' => $call->getReviewedEventIds(),
            'event_category_ids' => $call->getReviewedEventCategoryIds(),
            'risk_rank' => $call->getRiskRank(),
            'conversation_type' => $conversationType,
            'reviewers' => array_map(function (array $reviewer) {
                return $this->convertToMap([
                    'id' => (string) $reviewer['id'],
                    'name' => $reviewer['name'],
                ]);
            }, $call->getReviewers()),
            'comments' => $call->getComments()->reduce(function ($result, \STCall\Entity\Comment $comment) {
                $result[] = $this->convertToMap([
                    'comment_id' => $comment->getCommentId(),
                    'user_id' => (string) $comment->getUser()->getId(),
                    'user_name' => $comment->getUser()->getName(),
                    'date' => (string) $comment->getCreated(),
                    'text' => $comment->getMessageBodyWithMentions(),
                ]);
                return $result;
            }),
            'fragments' => $call->getFragments()->reduce(function ($result, \STCall\Entity\CallFragment\CallFragment $fragment) use ($call) {
                $result[] = $this->convertToMap([
                    'fragment_number' => (string) $call->getFragments()->search($fragment),
                    'start_time' => (string) $fragment->getStartTime(),
                    'end_time' => (string) $fragment->getEndTime(),
                    'active_paragraph_number' => (string) $fragment->getActiveParagraph()->getParagraphNumber(),
                    'fill_color_hex' => $fragment->getColor()->getFillHex(),
                    'outline_color_hex' => $fragment->getColor()->getOutlineHex(),
                ]);
                return $result;
            }),
        ];
    }

    /**
     *
     * @param int $roleId
     * @param Call $call
     * @param \STCompany\Entity\Event\CategoryCollection $eventCategories
     * @return array
     */
    protected function getPrecalculatedEventsRecord(
        int $roleId,
        Call $call,
        \STCompany\Entity\Event\CategoryCollection $eventCategories,
    ): array {
        $result = [];

        /** @var Paragraph $paragraph */
        foreach ($call->getParagraphs() as $paragraph) {
            /** @var EventHappening $eventHappening */
            foreach ($paragraph->getEventHappenings() as $eventHappening) {
                if ($eventHappening->isConfirmed()) {
                    /** @var Event $firstChangeEvent */
                    $firstChangeEvent = $eventHappening->getHistoryRecords()->first()->getEvent();

                    $result[] = [
                        'company_id' => $call->getCompanyId(),
                        'role_id' => $roleId,
                        'call_id' => $call->getId(),
                        'agent_id' => $call->getAgentId(),
                        'client_id' => $call->getClientId(),
                        'call_origin' => $call->getOrigin(),
                        'call_time' => $call->getTime(),
                        'paragraph' => $paragraph->getParagraphNumber(),
                        'paragraph_start_time' => $paragraph->getStartTime(),
                        'paragraph_speaker_role' => $paragraph->getSpeakerRole(),
                        'event_id' => $eventHappening->getEvent()->getId(),
                        'event_name' => $eventHappening->getEvent()->getName(),
                        'event_score' => $eventHappening->getEvent()->getScore(),
                        'event_highlight' => $eventHappening->getHighlight(),
                        'event_en_highlight' => $eventHappening->getEnHighlight(),
                        'event_text' => $paragraph->getText(),
                        'event_en_text' => $paragraph->getEnText(),
                        'event_icon' => $eventHappening->getEvent()->getIcon(),
                        'event_is_pinned' => (int) $eventHappening->getEvent()->isPinned(),
                        'event_is_deleted' => (int) $eventHappening->isDeleted(),
                        'event_category_id' => $eventHappening->getEvent()->getCategoryId(),
                        'event_category_name' => $eventCategories->offsetGet($eventHappening->getEvent()->getCategoryId())->getName(),
                        'event_color_id' => $eventHappening->getEvent()->getColor()->getId(),
                        'event_fill_color_hex' => $eventHappening->getEvent()->getColor()->getFillHex(),
                        'event_outline_color_hex' => $eventHappening->getEvent()->getColor()->getOutlineHex(),
                        'event_color_priority' => $eventHappening->getEvent()->getColor()->getPriority(),
                        'event_changed_from_event_id' => $firstChangeEvent?->getId(),
                    ];
                }
            }
        }
        return $result;
    }

    /**
     *
     * @param \STCompany\Entity\Company $company
     * @param array $roleIds
     * @param array $callIds
     * @param array $eventsPrecalculated
     * @return array
     */
    protected function reachByDeletedExistedPrecalculatedEventsRecord(
        \STCompany\Entity\Company $company,
        array $roleIds,
        array $callIds,
        array $eventsPrecalculated,
    ): array {
        $result = [];


        $eventsPrecalculatedHashTable = [];
        foreach ($eventsPrecalculated as $eventPrecalculated) {
            $eventsPrecalculatedHashTable[] = implode('-', [
                $eventPrecalculated['company_id'],
                $eventPrecalculated['role_id'],
                $eventPrecalculated['call_id'],
                $eventPrecalculated['paragraph'],
                $eventPrecalculated['event_id'],
            ]);
        }

        $existedRecords = $this->precalculatedCallsEventsTable->getPrecalculatedCalls($company, $roleIds, $callIds);
        foreach ($existedRecords as $existedRecord) {
            $recordHash = implode('-', [
                $existedRecord['company_id'],
                $existedRecord['role_id'],
                $existedRecord['call_id'],
                $existedRecord['paragraph'],
                $existedRecord['event_id'],
            ]);
            if (!in_array($recordHash, $eventsPrecalculatedHashTable)) {
                $existedRecord['event_is_deleted'] = 1;
                $result[] = $existedRecord;
            }
        }

        return $result;
    }
}
