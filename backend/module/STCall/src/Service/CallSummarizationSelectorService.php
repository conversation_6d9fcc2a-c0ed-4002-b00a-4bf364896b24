<?php

declare(strict_types=1);

namespace STCall\Service;

use ST<PERSON>all\Data\CallsSummarizationsTable;
use ST<PERSON>all\Data\CallSummarizationRepository;
use ST<PERSON>all\Entity\CallSummarization;

final class CallSummarizationSelectorService
{
    public function __construct(
        private readonly CallSummarizationRepository $callSummarizationRepository,
        private readonly CallsSummarizationsTable $callsSummarizationsTable,
    ) {
    }

    /**
     * @param string $callId
     * @param int $companyId
     * @return array
     */
    public function getExtendedCallSummarization(string $callId, int $companyId): array
    {
        return $this->callSummarizationRepository->getExtendedSummarizationData($callId, $companyId);
    }

    public function getCallSummarization(string $callId, int $companyId): CallSummarization
    {
        return $this->callsSummarizationsTable->getCallSummarization($callId, $companyId);
    }
}
